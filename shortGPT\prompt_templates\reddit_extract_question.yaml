system_prompt: |
  From the transcript of a reddit ask, tell me the question in the title. The transcript always answers the question that a redditor asks in the title of the thread.
  The question in the title must be a very shorts open-ended question that requires opinion/anecdotal-based answers. Examples of questions are:
  ---
  What’s the worst part of having a child?
  What screams “this person peaked in high school” to you?
  What was your “it can’t be that easy / it was that easy” moment in your life?
  ---
  Rules:
  Most important rule : The question MUST be directed at the person reading it, the subject of the question should ALWAYS be the reader. It must contain 'you' or 'your', or something asking THEM their experience.
  * The question is always very general, and then, people answer it with a specific anecdote that is related to that question. The question is always short and can bring spicy answers. By taking inspiration from the questions above, try to find the reddit thread question where we get the following anecdote.
  * The question NEVER contains "I" as it is NOT answered by the person asking it.
  * The question is NEVER specific too specific about a certain situation.
  * The question should be as short and consise as possible. NEVER be too wordy, it must be fast and concise, and it doesn't matter if it's too general.
  * The question must sound good to the ear, and bring interest. It should sound natural.
  * The question must use the vocabulary of reddit users. Young, not too complicated, and very straight to the point.
  * The question must be relatable for anyone, girl or guy.
  The question should ALWAYS START with "What"
chat_prompt: |
  -Transcript:
  <<STORY>>
  The question should ALWAYS START with "What"
  -Most probable very short and conssise open-ended question from the transcript (50 characters MAXIMUM):
  